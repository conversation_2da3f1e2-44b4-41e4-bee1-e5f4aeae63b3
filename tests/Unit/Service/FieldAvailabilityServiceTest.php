<?php

namespace Tests\Unit;

use App\Models\Field;
use App\Models\Reservation;
use App\Services\FieldAvailabilityService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Services\FieldAvailabilityService::class)]
class FieldAvailabilityServiceTest extends TestCase
{
    use RefreshDatabase;

    protected FieldAvailabilityService $service;

    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new FieldAvailabilityService;
        $this->field = Field::factory()->create([
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
            'status' => 'Active', // Explicitly set status for predictable tests
        ]);
    }

    #[Test]
    public function it_checks_if_field_is_available()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $startTime = '10:00';
        $endTime = '12:00';

        $this->assertTrue($this->service->isFieldAvailable($this->field, $date, $startTime, $endTime));
    }

    #[Test]
    public function it_detects_unavailable_field_due_to_existing_reservation()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create existing reservation
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Check overlapping time
        $this->assertFalse($this->service->isFieldAvailable($this->field, $date, '11:00', '13:00'));

        // Check non-overlapping time
        $this->assertTrue($this->service->isFieldAvailable($this->field, $date, '13:00', '15:00'));
    }

    #[Test]
    public function it_checks_working_hours_correctly()
    {
        // Within working hours
        $this->assertTrue($this->service->isWithinWorkingHours($this->field, '10:00', '12:00'));

        // Start before opening
        $this->assertFalse($this->service->isWithinWorkingHours($this->field, '07:00', '09:00'));

        // End after closing
        $this->assertFalse($this->service->isWithinWorkingHours($this->field, '21:00', '23:00'));

        // Exactly at boundaries
        $this->assertTrue($this->service->isWithinWorkingHours($this->field, '08:00', '22:00'));
    }

    #[Test]
    public function it_detects_conflicting_reservations()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create existing reservation
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Test various conflict scenarios
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '09:00', '11:00')); // Overlaps start
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '11:00', '13:00')); // Overlaps end
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '10:30', '11:30')); // Contained within
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '09:00', '13:00')); // Contains existing

        // No conflict
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '13:00', '15:00'));
    }

    #[Test]
    public function it_gets_available_time_slots()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $duration = 2;

        $slots = $this->service->getAvailableTimeSlots($this->field, $date, $duration);

        $this->assertIsArray($slots);
        $this->assertNotEmpty($slots);

        // Check slot structure
        $firstSlot = $slots[0];
        $this->assertArrayHasKey('start_time', $firstSlot);
        $this->assertArrayHasKey('end_time', $firstSlot);
        $this->assertArrayHasKey('display', $firstSlot);
        $this->assertArrayHasKey('value', $firstSlot);
    }

    #[Test]
    public function it_excludes_unavailable_slots_from_available_time_slots()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create existing reservation
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        $slots = $this->service->getAvailableTimeSlots($this->field, $date, 2);

        // Should not include slots that would conflict
        $conflictingSlots = array_filter($slots, function ($slot) {
            return $slot['start_time'] === '10:00' || $slot['start_time'] === '09:00';
        });

        $this->assertEmpty($conflictingSlots);
    }

    #[Test]
    public function it_includes_excluded_reservation_slot_when_exclude_reservation_id_provided()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create existing reservation that we want to exclude
        $existingReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Without exclude_reservation_id, the slot should not be available
        $slotsWithoutExclude = $this->service->getAvailableTimeSlots($this->field, $date, 2);
        $conflictingSlots = array_filter($slotsWithoutExclude, function ($slot) {
            return $slot['start_time'] === '10:00';
        });
        $this->assertEmpty($conflictingSlots, 'Slot should not be available without exclude parameter');

        // With exclude_reservation_id, the slot should be available
        $slotsWithExclude = $this->service->getAvailableTimeSlots($this->field, $date, 2, $existingReservation->id);
        $availableSlots = array_filter($slotsWithExclude, function ($slot) {
            return $slot['start_time'] === '10:00';
        });
        $this->assertNotEmpty($availableSlots, 'Slot should be available when excluding the reservation');

        // Verify the slot structure
        $slot = reset($availableSlots);
        $this->assertEquals('10:00', $slot['start_time']);
        $this->assertEquals('12:00', $slot['end_time']);
        $this->assertArrayHasKey('display', $slot);
        $this->assertArrayHasKey('value', $slot);
    }

    #[Test]
    public function it_gets_field_availability_calendar()
    {
        $startDate = Carbon::now()->addDays(1);
        $endDate = Carbon::now()->addDays(7);

        $calendar = $this->service->getFieldAvailabilityCalendar($this->field, $startDate, $endDate);

        $this->assertIsArray($calendar);
        $this->assertCount(7, $calendar); // 7 days

        $firstDay = array_values($calendar)[0];
        $this->assertArrayHasKey('date', $firstDay);
        $this->assertArrayHasKey('day_name', $firstDay);
        $this->assertArrayHasKey('formatted_date', $firstDay);
        $this->assertArrayHasKey('available_slots', $firstDay);
        $this->assertArrayHasKey('slots', $firstDay);
        $this->assertArrayHasKey('is_past', $firstDay);
        $this->assertArrayHasKey('is_today', $firstDay);
    }

    #[Test]
    public function it_gets_all_fields_availability()
    {
        // Create 2 additional active fields (plus the one from setUp() = 3 total)
        Field::factory()->create(['status' => 'Active']);
        Field::factory()->create(['status' => 'Active']);
        $date = now()->addDays(1)->format('Y-m-d');

        $availability = $this->service->getAllFieldsAvailability($date, 2);

        $this->assertIsArray($availability);
        $this->assertCount(3, $availability); // Three active fields (1 from setUp + 2 created here)

        $firstFieldAvailability = $availability[0];
        $this->assertArrayHasKey('field', $firstFieldAvailability);
        $this->assertArrayHasKey('available_slots', $firstFieldAvailability);
        $this->assertArrayHasKey('total_slots', $firstFieldAvailability);
        $this->assertArrayHasKey('is_available', $firstFieldAvailability);
    }

    #[Test]
    public function it_validates_duration()
    {
        $this->assertTrue($this->service->isValidDuration($this->field, 3)); // Within range
        $this->assertFalse($this->service->isValidDuration($this->field, 0)); // Below minimum
        $this->assertFalse($this->service->isValidDuration($this->field, 10)); // Above maximum
    }

    #[Test]
    public function it_finds_next_available_slot()
    {
        $fromDate = Carbon::now()->addDays(1);

        $nextSlot = $this->service->getNextAvailableSlot($this->field, 2, $fromDate);

        $this->assertIsArray($nextSlot);
        $this->assertArrayHasKey('date', $nextSlot);
        $this->assertArrayHasKey('formatted_date', $nextSlot);
        $this->assertArrayHasKey('slot', $nextSlot);
    }

    #[Test]
    public function it_validates_reservation_comprehensively()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Valid reservation
        $validation = $this->service->canMakeReservation($this->field, $date, '10:00', 2);
        $this->assertTrue($validation['can_reserve']);
        $this->assertEmpty($validation['errors']);

        // Invalid - past date
        $pastDate = now()->subDays(1)->format('Y-m-d');
        $validation = $this->service->canMakeReservation($this->field, $pastDate, '10:00', 2);
        $this->assertFalse($validation['can_reserve']);
        $this->assertNotEmpty($validation['errors']);

        // Invalid - inactive field
        $this->field->update(['status' => 'Inactive']);
        $validation = $this->service->canMakeReservation($this->field, $date, '10:00', 2);
        $this->assertFalse($validation['can_reserve']);
        $this->assertNotEmpty($validation['errors']);
    }

    #[Test]
    public function it_gets_field_statistics()
    {
        $fromDate = Carbon::now()->startOfMonth();
        $toDate = Carbon::now()->endOfMonth();

        // Use a date that's definitely within the range
        $bookingDate = $fromDate->copy()->addDays(5)->format('Y-m-d');

        // Create test reservations
        Reservation::factory()->count(3)->create([
            'field_id' => $this->field->id,
            'booking_date' => $bookingDate,
            'status' => 'Confirmed',
            'total_cost' => 100.00,
            'duration_hours' => 2,
        ]);

        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $bookingDate,
            'status' => 'Cancelled',
            'total_cost' => 100.00,
            'duration_hours' => 2,
        ]);

        $stats = $this->service->getFieldStatistics($this->field, $fromDate, $toDate);

        $this->assertArrayHasKey('total_reservations', $stats);
        $this->assertArrayHasKey('confirmed_reservations', $stats);
        $this->assertArrayHasKey('cancelled_reservations', $stats);
        $this->assertArrayHasKey('cancellation_rate', $stats);
        $this->assertArrayHasKey('total_revenue', $stats);
        $this->assertArrayHasKey('total_hours', $stats);
        $this->assertArrayHasKey('average_reservation_value', $stats);

        $this->assertEquals(4, $stats['total_reservations']);
        $this->assertEquals(3, $stats['confirmed_reservations']);
        $this->assertEquals(1, $stats['cancelled_reservations']);
        $this->assertEquals(25.0, $stats['cancellation_rate']); // 1/4 * 100
        $this->assertEquals(300.00, $stats['total_revenue']); // 3 * 100
        $this->assertEquals(6, $stats['total_hours']); // 3 * 2
    }
}
