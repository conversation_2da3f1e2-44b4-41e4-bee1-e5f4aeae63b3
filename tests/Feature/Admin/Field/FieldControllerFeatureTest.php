<?php

namespace Tests\Feature\Admin;

use App\Http\Controllers\Admin\FieldController;
use App\Models\Amenity;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(FieldController::class)]
class FieldControllerFeatureTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;

    protected User $employee;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users with different roles
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->employee = User::factory()->create(['role' => 'employee']);
        $this->user = User::factory()->create(['role' => 'user']);
    }

    #[Test]
    public function admin_can_view_fields_index()
    {
        // Ensure clean database state for this test
        Field::query()->delete();

        // Create test fields with controlled timestamps for deterministic ordering
        for ($i = 1; $i <= 5; $i++) {
            Field::factory()->create([
                'name' => "Test Field {$i}",
                'description' => "Test field {$i} description",
                'created_at' => now()->subMinutes(6 - $i), // Ensures predictable ordering
            ]);
        }

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.fields.index');
        $response->assertViewHas('fields');

        // Assert pagination is working
        $fields = $response->viewData('fields');
        $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $fields);
        $this->assertEquals(5, $fields->count());
    }

    #[Test]
    public function admin_can_search_fields_by_name()
    {
        // Ensure clean database state for this test
        Field::query()->delete();

        // Create test fields with specific names and controlled attributes to avoid factory randomness
        $soccerField1 = Field::factory()->create([
            'name' => 'Soccer Field Alpha',
            'type' => 'Soccer',
            'description' => 'Test soccer field alpha description',
            'created_at' => now()->subMinutes(3),
        ]);

        $basketballField = Field::factory()->create([
            'name' => 'Basketball Court Beta',
            'type' => 'Basketball',
            'description' => 'Test basketball court beta description',
            'created_at' => now()->subMinutes(2),
        ]);

        $soccerField2 = Field::factory()->create([
            'name' => 'Soccer Field Gamma',
            'type' => 'Soccer',
            'description' => 'Test soccer field gamma description',
            'created_at' => now()->subMinutes(1),
        ]);

        // Ensure database changes are committed
        $this->artisan('cache:clear');

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index', ['search' => 'Soccer']));

        $response->assertStatus(200);

        // More specific assertions to check the actual content
        $responseContent = $response->getContent();
        $this->assertStringContainsString('Soccer Field Alpha', $responseContent);
        $this->assertStringContainsString('Soccer Field Gamma', $responseContent);
        $this->assertStringNotContainsString('Basketball Court Beta', $responseContent);

        // Also verify the view data directly
        $fields = $response->viewData('fields');
        $fieldNames = $fields->pluck('name')->toArray();
        $this->assertContains('Soccer Field Alpha', $fieldNames);
        $this->assertContains('Soccer Field Gamma', $fieldNames);
        $this->assertNotContains('Basketball Court Beta', $fieldNames);
        $this->assertCount(2, $fields);
    }

    #[Test]
    public function admin_can_filter_fields_by_type()
    {
        // Ensure clean database state for this test
        Field::query()->delete();

        // Create test fields with different types and controlled timestamps
        Field::factory()->create([
            'type' => 'Soccer',
            'name' => 'Soccer Field 1',
            'description' => 'Test soccer field 1 description',
            'created_at' => now()->subMinutes(3),
        ]);
        Field::factory()->create([
            'type' => 'Basketball',
            'name' => 'Basketball Court 1',
            'description' => 'Test basketball court 1 description',
            'created_at' => now()->subMinutes(2),
        ]);
        Field::factory()->create([
            'type' => 'Soccer',
            'name' => 'Soccer Field 2',
            'description' => 'Test soccer field 2 description',
            'created_at' => now()->subMinutes(1),
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index', ['type' => 'Soccer']));

        $response->assertStatus(200);

        // Verify view data directly for more reliable assertions
        $fields = $response->viewData('fields');
        $fieldNames = $fields->pluck('name')->toArray();
        $this->assertContains('Soccer Field 1', $fieldNames);
        $this->assertContains('Soccer Field 2', $fieldNames);
        $this->assertNotContains('Basketball Court 1', $fieldNames);
        $this->assertCount(2, $fields);

        // Also check response content
        $response->assertSee('Soccer Field 1');
        $response->assertSee('Soccer Field 2');
        $response->assertDontSee('Basketball Court 1');
    }

    #[Test]
    public function admin_can_filter_fields_by_status()
    {
        // Ensure clean database state for this test
        Field::query()->delete();

        // Create test fields with different statuses and controlled timestamps
        Field::factory()->create([
            'status' => 'Active',
            'name' => 'Active Field 1',
            'description' => 'Test active field 1 description',
            'created_at' => now()->subMinutes(3),
        ]);
        Field::factory()->create([
            'status' => 'Inactive',
            'name' => 'Inactive Field 1',
            'description' => 'Test inactive field 1 description',
            'created_at' => now()->subMinutes(2),
        ]);
        Field::factory()->create([
            'status' => 'Active',
            'name' => 'Active Field 2',
            'description' => 'Test active field 2 description',
            'created_at' => now()->subMinutes(1),
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index', ['status' => 'Active']));

        $response->assertStatus(200);

        // Verify view data directly for more reliable assertions
        $fields = $response->viewData('fields');
        $fieldNames = $fields->pluck('name')->toArray();
        $this->assertContains('Active Field 1', $fieldNames);
        $this->assertContains('Active Field 2', $fieldNames);
        $this->assertNotContains('Inactive Field 1', $fieldNames);
        $this->assertCount(2, $fields);

        // Also check response content
        $response->assertSee('Active Field 1');
        $response->assertSee('Active Field 2');
        $response->assertDontSee('Inactive Field 1');
    }

    #[Test]
    public function admin_can_view_create_field_form()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.create'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.fields.create');
    }

    #[Test]
    public function admin_can_create_field_with_valid_data()
    {
        // Create test amenities and utilities
        $amenities = Amenity::factory()->count(2)->create();
        $utilities = Utility::factory()->count(2)->create();

        $fieldData = [
            'name' => 'New Test Field',
            'type' => 'Soccer',
            'description' => 'A brand new test field for soccer',
            'hourly_rate' => 75.50,
            'night_hourly_rate' => 95.00,
            'night_time_start' => '18:30',
            'capacity' => 25,
            'status' => 'Active',
            'amenities' => $amenities->pluck('id')->toArray(),
            'utilities' => $utilities->pluck('id')->toArray(),
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), $fieldData);

        $response->assertRedirect(route('admin.fields.index'));
        $response->assertSessionHas('success', 'Field created successfully.');

        // Assert the field was created in the database
        $this->assertDatabaseHas('fields', [
            'name' => 'New Test Field',
            'type' => 'Soccer',
            'description' => 'A brand new test field for soccer',
            'hourly_rate' => 75.50,
            'night_hourly_rate' => 95.00,
            'night_time_start' => '18:30',
            'capacity' => 25,
            'status' => 'Active',
        ]);

        // Assert relationships were created
        $field = Field::where('name', 'New Test Field')->first();
        $this->assertCount(2, $field->amenities);
        $this->assertCount(2, $field->utilities);
    }

    #[Test]
    public function admin_cannot_create_field_with_invalid_data()
    {
        $invalidData = [
            'name' => '', // Required field missing
            'type' => 'InvalidType', // Invalid type
            'hourly_rate' => -10, // Negative rate
            'capacity' => 0, // Below minimum
            'status' => 'InvalidStatus', // Invalid status
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), $invalidData);

        $response->assertRedirect();
        $response->assertSessionHasErrors([
            'name',
            'type',
            'hourly_rate',
            'capacity',
            'status',
        ]);

        // Assert no field was created
        $this->assertDatabaseMissing('fields', ['name' => '']);
    }

    #[Test]
    public function admin_cannot_create_field_with_duplicate_name()
    {
        // Create an existing field
        Field::factory()->create(['name' => 'Existing Field']);

        $fieldData = [
            'name' => 'Existing Field', // Duplicate name
            'type' => 'Soccer',
            'description' => 'Test description',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), $fieldData);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['name']);
    }

    #[Test]
    public function admin_can_view_field_details()
    {
        // Create a field with relationships
        $field = Field::factory()->create([
            'name' => 'Test Field Details',
            'type' => 'Soccer',
            'description' => 'Detailed test field',
        ]);

        $amenities = Amenity::factory()->count(2)->create();
        $utilities = Utility::factory()->count(2)->create();
        $field->amenities()->attach($amenities->pluck('id'));
        $field->utilities()->attach($utilities->pluck('id'));

        // Create some bookings for the field
        Reservation::factory()->count(3)->create([
            'field_id' => $field->id,
            'user_id' => $this->user->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $field));

        $response->assertStatus(200);
        $response->assertViewIs('admin.fields.show');
        $response->assertViewHas('field');
        $response->assertSee('Test Field Details');
        $response->assertSee('Detailed test field');
    }

    #[Test]
    public function admin_can_view_edit_field_form()
    {
        $field = Field::factory()->create(['name' => 'Field to Edit']);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.edit', $field));

        $response->assertStatus(200);
        $response->assertViewIs('admin.fields.edit');
        $response->assertViewHas('field');
        $response->assertSee('Field to Edit');
    }

    #[Test]
    public function admin_can_update_field_with_valid_data()
    {
        // Create a field with existing relationships
        $field = Field::factory()->create([
            'name' => 'Original Field Name',
            'type' => 'Soccer',
            'description' => 'Original description',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
        ]);

        $originalAmenities = Amenity::factory()->count(2)->create();
        $originalUtilities = Utility::factory()->count(2)->create();
        $field->amenities()->attach($originalAmenities->pluck('id'));
        $field->utilities()->attach($originalUtilities->pluck('id'));

        // Create new amenities and utilities for the update
        $newAmenities = Amenity::factory()->count(3)->create();
        $newUtilities = Utility::factory()->count(3)->create();

        $updateData = [
            'name' => 'Updated Field Name',
            'type' => 'Basketball',
            'description' => 'Updated description',
            'hourly_rate' => 85.00,
            'night_hourly_rate' => 110.00,
            'night_time_start' => '19:00',
            'capacity' => 30,
            'status' => 'Inactive',
            'amenities' => $newAmenities->pluck('id')->toArray(),
            'utilities' => $newUtilities->pluck('id')->toArray(),
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.fields.update', $field), $updateData);

        $response->assertRedirect(route('admin.fields.show', $field));
        $response->assertSessionHas('success', 'Field updated successfully.');

        // Assert the field was updated in the database
        $this->assertDatabaseHas('fields', [
            'id' => $field->id,
            'name' => 'Updated Field Name',
            'type' => 'Basketball',
            'description' => 'Updated description',
            'hourly_rate' => 85.00,
            'night_hourly_rate' => 110.00,
            'night_time_start' => '19:00',
            'capacity' => 30,
            'status' => 'Inactive',
        ]);

        // Assert relationships were updated
        $field->refresh();
        $this->assertCount(3, $field->amenities);
        $this->assertCount(3, $field->utilities);
        $this->assertEquals($newAmenities->pluck('id')->sort()->values(), $field->amenities->pluck('id')->sort()->values());
        $this->assertEquals($newUtilities->pluck('id')->sort()->values(), $field->utilities->pluck('id')->sort()->values());
    }

    #[Test]
    public function admin_cannot_update_field_with_invalid_data()
    {
        $field = Field::factory()->create(['name' => 'Original Field']);

        $invalidData = [
            'name' => '', // Required field missing
            'type' => 'InvalidType', // Invalid type
            'hourly_rate' => -10, // Negative rate
            'capacity' => 0, // Below minimum
            'status' => 'InvalidStatus', // Invalid status
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.fields.update', $field), $invalidData);

        $response->assertRedirect();
        $response->assertSessionHasErrors([
            'name',
            'type',
            'hourly_rate',
            'capacity',
            'status',
        ]);

        // Assert the field was not updated
        $field->refresh();
        $this->assertEquals('Original Field', $field->name);
    }

    #[Test]
    public function admin_cannot_update_field_with_duplicate_name()
    {
        // Create two fields
        $field1 = Field::factory()->create(['name' => 'Field One']);
        $field2 = Field::factory()->create(['name' => 'Field Two']);

        $updateData = [
            'name' => 'Field One', // Duplicate name
            'type' => 'Soccer',
            'description' => 'Test description',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.fields.update', $field2), $updateData);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['name']);

        // Assert the field was not updated
        $field2->refresh();
        $this->assertEquals('Field Two', $field2->name);
    }

    #[Test]
    public function admin_can_delete_field_without_active_bookings()
    {
        $field = Field::factory()->create(['name' => 'Field to Delete']);

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.fields.destroy', $field));

        $response->assertRedirect(route('admin.fields.index'));
        $response->assertSessionHas('success');

        // Assert the field was soft deleted
        $this->assertSoftDeleted('fields', ['id' => $field->id]);
    }

    #[Test]
    public function admin_cannot_delete_field_with_active_bookings()
    {
        $field = Field::factory()->create(['name' => 'Field with Bookings']);

        // Create active bookings for the field
        Reservation::factory()->count(2)->create([
            'field_id' => $field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed', // Active booking
        ]);

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.fields.destroy', $field));

        $response->assertRedirect();
        $response->assertSessionHas('error');

        // Assert the field was not deleted
        $this->assertDatabaseHas('fields', ['id' => $field->id]);
        $this->assertNotSoftDeleted('fields', ['id' => $field->id]);
    }

    #[Test]
    public function employee_cannot_access_field_management()
    {
        $response = $this->actingAs($this->employee)
            ->get(route('admin.fields.index'));

        $response->assertStatus(403);
    }

    #[Test]
    public function user_cannot_access_field_management()
    {
        $response = $this->actingAs($this->user)
            ->get(route('admin.fields.index'));

        $response->assertStatus(403);
    }

    #[Test]
    public function unauthenticated_user_cannot_access_field_management()
    {
        $response = $this->get(route('admin.fields.index'));

        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function admin_can_create_field_with_amenities_and_utilities()
    {
        // Create test amenities and utilities
        $amenities = Amenity::factory()->count(3)->create();
        $utilities = Utility::factory()->count(2)->create();

        $fieldData = [
            'name' => 'Field with Relationships',
            'type' => 'Soccer',
            'description' => 'Test field with amenities and utilities',
            'hourly_rate' => 60.00,
            'capacity' => 22,
            'status' => 'Active',
            'amenities' => $amenities->pluck('id')->toArray(),
            'utilities' => $utilities->pluck('id')->toArray(),
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), $fieldData);

        $response->assertRedirect(route('admin.fields.index'));

        // Get the created field
        $field = Field::where('name', 'Field with Relationships')->first();

        // Assert relationships were created correctly
        $this->assertCount(3, $field->amenities);
        $this->assertCount(2, $field->utilities);

        // Assert the correct amenities and utilities are attached
        $this->assertEquals($amenities->pluck('id')->sort()->values(), $field->amenities->pluck('id')->sort()->values());
        $this->assertEquals($utilities->pluck('id')->sort()->values(), $field->utilities->pluck('id')->sort()->values());
    }

    #[Test]
    public function admin_can_create_field_without_amenities_and_utilities()
    {
        $fieldData = [
            'name' => 'Simple Field',
            'type' => 'Tennis',
            'description' => 'Simple field without amenities',
            'hourly_rate' => 40.00,
            'capacity' => 4,
            'status' => 'Active',
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), $fieldData);

        $response->assertRedirect(route('admin.fields.index'));

        // Get the created field
        $field = Field::where('name', 'Simple Field')->first();

        // Assert no relationships were created
        $this->assertCount(0, $field->amenities);
        $this->assertCount(0, $field->utilities);
    }

    #[Test]
    public function admin_can_update_field_relationships()
    {
        // Create a field with existing relationships
        $field = Field::factory()->create(['name' => 'Field to Update']);
        $originalAmenities = Amenity::factory()->count(2)->create();
        $originalUtilities = Utility::factory()->count(2)->create();
        $field->amenities()->attach($originalAmenities->pluck('id'));
        $field->utilities()->attach($originalUtilities->pluck('id'));

        // Create new relationships
        $newAmenities = Amenity::factory()->count(3)->create();
        $newUtilities = Utility::factory()->count(1)->create();

        $updateData = [
            'name' => 'Updated Field',
            'type' => 'Basketball',
            'description' => 'Updated field description',
            'hourly_rate' => 70.00,
            'capacity' => 20,
            'status' => 'Active',
            'amenities' => $newAmenities->pluck('id')->toArray(),
            'utilities' => $newUtilities->pluck('id')->toArray(),
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.fields.update', $field), $updateData);

        $response->assertRedirect(route('admin.fields.show', $field));

        // Refresh the field and check relationships
        $field->refresh();
        $this->assertCount(3, $field->amenities);
        $this->assertCount(1, $field->utilities);

        // Assert the new relationships are correct
        $this->assertEquals($newAmenities->pluck('id')->sort()->values(), $field->amenities->pluck('id')->sort()->values());
        $this->assertEquals($newUtilities->pluck('id')->sort()->values(), $field->utilities->pluck('id')->sort()->values());
    }

    #[Test]
    public function admin_can_remove_all_field_relationships()
    {
        // Create a field with existing relationships
        $field = Field::factory()->create(['name' => 'Field with Relationships']);
        $amenities = Amenity::factory()->count(2)->create();
        $utilities = Utility::factory()->count(2)->create();
        $field->amenities()->attach($amenities->pluck('id'));
        $field->utilities()->attach($utilities->pluck('id'));

        $updateData = [
            'name' => 'Field without Relationships',
            'type' => 'Soccer',
            'description' => 'Field with no amenities or utilities',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
            // No amenities or utilities provided
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.fields.update', $field), $updateData);

        $response->assertRedirect(route('admin.fields.show', $field));

        // Refresh the field and check relationships were removed
        $field->refresh();
        $this->assertCount(0, $field->amenities);
        $this->assertCount(0, $field->utilities);
    }
}
