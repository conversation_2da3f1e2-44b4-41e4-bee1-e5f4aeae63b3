<?php

namespace Tests\Feature\Admin;

use App\Http\Controllers\Admin\FieldController;
use App\Models\Amenity;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(FieldController::class)]
class FieldControllerEdgeCasesTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        $this->admin = User::factory()->create(['role' => 'admin']);
    }

    #[Test]
    public function index_handles_empty_database()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.fields.index');

        $fields = $response->viewData('fields');
        $this->assertEquals(0, $fields->count());
    }

    #[Test]
    public function index_handles_search_with_no_results()
    {
        Field::factory()->count(3)->create();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index', ['search' => 'NonExistentField']));

        $response->assertStatus(200);

        $fields = $response->viewData('fields');
        $this->assertEquals(0, $fields->count());
    }

    #[Test]
    public function index_handles_filter_with_no_results()
    {
        Field::factory()->count(3)->create(['type' => 'Soccer']);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index', ['type' => 'Cricket']));

        $response->assertStatus(200);

        $fields = $response->viewData('fields');
        $this->assertEquals(0, $fields->count());
    }

    #[Test]
    public function index_handles_multiple_filters_simultaneously()
    {
        // Create fields with different combinations
        Field::factory()->create(['type' => 'Soccer', 'status' => 'Active', 'name' => 'Soccer Field A']);
        Field::factory()->create(['type' => 'Soccer', 'status' => 'Inactive', 'name' => 'Soccer Field B']);
        Field::factory()->create(['type' => 'Basketball', 'status' => 'Active', 'name' => 'Basketball Court']);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index', [
                'search' => 'Soccer',
                'type' => 'Soccer',
                'status' => 'Active',
            ]));

        $response->assertStatus(200);

        $fields = $response->viewData('fields');
        $this->assertEquals(1, $fields->count());
        $this->assertEquals('Soccer Field A', $fields->first()->name);
    }

    #[Test]
    public function store_handles_empty_amenities_array()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Field with Empty Amenities',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'Active',
                'amenities' => [],
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
            ]);

        $response->assertRedirect(route('admin.fields.index'));

        $field = Field::where('name', 'Field with Empty Amenities')->first();
        $this->assertCount(0, $field->amenities);
    }

    #[Test]
    public function store_handles_empty_utilities_array()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Field with Empty Utilities',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'Active',
                'utilities' => [],
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
            ]);

        $response->assertRedirect(route('admin.fields.index'));

        $field = Field::where('name', 'Field with Empty Utilities')->first();
        $this->assertCount(0, $field->utilities);
    }

    #[Test]
    public function store_handles_null_optional_fields()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Field with Nulls',
                'type' => 'Soccer',
                'description' => null,
                'hourly_rate' => 50.00,
                'night_hourly_rate' => null,
                'night_time_start' => null,
                'capacity' => 20,
                'status' => 'Active',
                'amenities' => null,
                'utilities' => null,
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
            ]);

        $response->assertRedirect(route('admin.fields.index'));

        $field = Field::where('name', 'Field with Nulls')->first();
        $this->assertNull($field->description);
        $this->assertNull($field->night_hourly_rate);
        $this->assertEquals('18:00', $field->night_time_start); // Default value
        $this->assertCount(0, $field->amenities);
        $this->assertCount(0, $field->utilities);
    }

    #[Test]
    public function update_handles_unique_name_validation_ignoring_current_field()
    {
        $field1 = Field::factory()->create(['name' => 'Field One']);
        $field2 = Field::factory()->create(['name' => 'Field Two']);

        // Update field2 to keep its own name (should be allowed)
        $response = $this->actingAs($this->admin)
            ->put(route('admin.fields.update', $field2), [
                'name' => 'Field Two',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'Active',
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
            ]);

        $response->assertRedirect(route('admin.fields.show', $field2));
        $response->assertSessionHas('success');
    }

    #[Test]
    public function destroy_handles_field_with_cancelled_bookings()
    {
        $field = Field::factory()->create(['name' => 'Field with Cancelled Bookings']);

        // Create cancelled bookings (should not prevent deletion)
        Reservation::factory()->count(2)->create([
            'field_id' => $field->id,
            'user_id' => $this->admin->id,
            'status' => 'Cancelled',
        ]);

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.fields.destroy', $field));

        $response->assertRedirect(route('admin.fields.index'));
        $response->assertSessionHas('success');

        $this->assertSoftDeleted('fields', ['id' => $field->id]);
    }

    #[Test]
    public function destroy_handles_field_with_mixed_booking_statuses()
    {
        $field = Field::factory()->create(['name' => 'Field with Mixed Bookings']);

        // Create mix of active and cancelled bookings
        Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->admin->id,
            'status' => 'Confirmed', // Active
        ]);

        Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->admin->id,
            'status' => 'Cancelled', // Not active
        ]);

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.fields.destroy', $field));

        $response->assertRedirect();
        $response->assertSessionHas('error');

        $this->assertNotSoftDeleted('fields', ['id' => $field->id]);
    }

    #[Test]
    public function show_handles_field_with_no_bookings()
    {
        $field = Field::factory()->create(['name' => 'Field with No Bookings']);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $field));

        $response->assertStatus(200);
        $response->assertViewIs('admin.fields.show');

        $responseField = $response->viewData('field');
        $this->assertTrue($responseField->relationLoaded('bookings'));
        $this->assertCount(0, $responseField->bookings);
    }

    #[Test]
    public function show_handles_field_with_no_amenities_or_utilities()
    {
        $field = Field::factory()->create(['name' => 'Simple Field']);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $field));

        $response->assertStatus(200);

        $responseField = $response->viewData('field');
        $this->assertTrue($responseField->relationLoaded('amenities'));
        $this->assertTrue($responseField->relationLoaded('utilities'));
        $this->assertCount(0, $responseField->amenities);
        $this->assertCount(0, $responseField->utilities);
    }

    #[Test]
    public function edit_handles_field_with_no_relationships()
    {
        $field = Field::factory()->create(['name' => 'Simple Field']);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.edit', $field));

        $response->assertStatus(200);
        $response->assertViewIs('admin.fields.edit');

        $responseField = $response->viewData('field');
        $this->assertTrue($responseField->relationLoaded('amenities'));
        $this->assertTrue($responseField->relationLoaded('utilities'));
        $this->assertCount(0, $responseField->amenities);
        $this->assertCount(0, $responseField->utilities);
    }

    #[Test]
    public function update_handles_removing_all_relationships()
    {
        // Create field with relationships
        $field = Field::factory()->create(['name' => 'Field with Relationships']);
        $amenities = Amenity::factory()->count(3)->create();
        $utilities = Utility::factory()->count(2)->create();
        $field->amenities()->attach($amenities->pluck('id'));
        $field->utilities()->attach($utilities->pluck('id'));

        // Update without any relationships
        $response = $this->actingAs($this->admin)
            ->put(route('admin.fields.update', $field), [
                'name' => 'Field without Relationships',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'Active',
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
                // No amenities or utilities
            ]);

        $response->assertRedirect(route('admin.fields.show', $field));

        $field->refresh();
        $this->assertCount(0, $field->amenities);
        $this->assertCount(0, $field->utilities);
    }

    #[Test]
    public function store_handles_maximum_valid_values()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => str_repeat('a', 255), // Maximum length
                'type' => 'Multi-Purpose',
                'description' => str_repeat('b', 1000), // Maximum length
                'hourly_rate' => 9999.99, // Maximum value
                'night_hourly_rate' => 9999.99, // Maximum value
                'night_time_start' => '23:59',
                'capacity' => 1000, // Maximum value
                'status' => 'Under Maintenance',
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
            ]);

        $response->assertRedirect(route('admin.fields.index'));
        $response->assertSessionHas('success');
    }

    #[Test]
    public function store_handles_minimum_valid_values()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'A', // Minimum length
                'type' => 'Soccer',
                'hourly_rate' => 0.00, // Minimum value
                'night_hourly_rate' => 0.00, // Minimum value
                'night_time_start' => '00:00',
                'capacity' => 1, // Minimum value
                'status' => 'Active',
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
            ]);

        $response->assertRedirect(route('admin.fields.index'));
        $response->assertSessionHas('success');
    }

    #[Test]
    public function index_pagination_works_correctly()
    {
        // Create more than 15 fields (default pagination limit)
        Field::factory()->count(20)->create();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index'));

        $response->assertStatus(200);

        $fields = $response->viewData('fields');
        $this->assertEquals(15, $fields->count()); // Should show 15 per page
        $this->assertEquals(20, $fields->total()); // Total should be 20
        $this->assertEquals(2, $fields->lastPage()); // Should have 2 pages
    }

    #[Test]
    public function index_maintains_search_parameters_in_pagination()
    {
        // Create fields with specific names
        Field::factory()->count(20)->create(['name' => 'Soccer Field Test']);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index', ['search' => 'Soccer']));

        $response->assertStatus(200);

        $fields = $response->viewData('fields');
        $this->assertEquals(15, $fields->count()); // Should show 15 per page
        $this->assertEquals(20, $fields->total()); // Total should be 20

        // Check that pagination links maintain the search parameter
        $paginationView = $fields->appends(['search' => 'Soccer'])->links()->toHtml();
        $this->assertStringContainsString('search=Soccer', $paginationView);
    }
}
